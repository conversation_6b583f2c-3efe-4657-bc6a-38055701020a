/*
 Navicat Premium Data Transfer

 Source Server         : localhost_3306
 Source Server Type    : MySQL
 Source Server Version : 80300 (8.3.0)
 Source Host           : localhost:3306
 Source Schema         : study_tour_system

 Target Server Type    : MySQL
 Target Server Version : 80300 (8.3.0)
 File Encoding         : 65001

 Date: 26/06/2024 19:35:52
*/

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `study_tour_system` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 使用数据库
USE `study_tour_system`;

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for article_comments
-- ----------------------------
DROP TABLE IF EXISTS `article_comments`;
CREATE TABLE `article_comments` (
  `comment_id` int NOT NULL AUTO_INCREMENT,
  `article_id` int NOT NULL,
  `user_id` int NOT NULL,
  `content` text NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`comment_id`),
  KEY `article_id` (`article_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `article_comments_ibfk_1` FOREIGN KEY (`article_id`) REFERENCES `articles` (`article_id`),
  CONSTRAINT `article_comments_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of article_comments
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for article_scores
-- ----------------------------
DROP TABLE IF EXISTS `article_scores`;
CREATE TABLE `article_scores` (
  `user_id` int NOT NULL,
  `article_id` int NOT NULL,
  `score` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`user_id`,`article_id`),
  KEY `article_id` (`article_id`),
  CONSTRAINT `article_scores_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`),
  CONSTRAINT `article_scores_ibfk_2` FOREIGN KEY (`article_id`) REFERENCES `articles` (`article_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of article_scores
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for articles
-- ----------------------------
DROP TABLE IF EXISTS `articles`;
CREATE TABLE `articles` (
  `article_id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `location` varchar(255) DEFAULT NULL,
  `content` blob NOT NULL,
  `huffman_codes` text,
  `tags` text COMMENT '文章标签，JSON格式存储',
  `popularity` int DEFAULT '0',
  `evaluation` double DEFAULT '0',
  `user_id` int NOT NULL,
  `image_url` varchar(255) DEFAULT NULL,
  `video_url` varchar(255) DEFAULT NULL,
  `image_url_2` varchar(255) DEFAULT NULL,
  `image_url_3` varchar(255) DEFAULT NULL,
  `image_url_4` varchar(255) DEFAULT NULL,
  `image_url_5` varchar(255) DEFAULT NULL,
  `image_url_6` varchar(255) DEFAULT NULL,
  `video_url_2` varchar(255) DEFAULT NULL,
  `video_url_3` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`article_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `articles_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of articles
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for edges
-- ----------------------------
DROP TABLE IF EXISTS `edges`;
CREATE TABLE `edges` (
  `edge_id` int NOT NULL AUTO_INCREMENT,
  `src_id` int DEFAULT NULL,
  `dest_id` int DEFAULT NULL,
  `weight` int DEFAULT NULL,
  `crowding` double DEFAULT NULL,
  `is_rideable` bit(1) DEFAULT NULL,
  `f7` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`edge_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3060 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of edges
-- ----------------------------
BEGIN;
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (1, 1, 2, 140, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (2, 1, 99, 40, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (3, 1, 99, 25, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (4, 2, 4, 10, 0.2, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (5, 2, 105, 330, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (6, 3, 4, 75, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (7, 3, 7, 40, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (8, 4, 9, 40, 0.2, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (9, 5, 6, 30, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (10, 6, 7, 30, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (11, 7, 8, 40, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (12, 7, 12, 20, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (13, 8, 9, 40, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (14, 9, 13, 20, 0.2, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (15, 10, 11, 40, 1, 0);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (16, 11, 16, 15, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (17, 12, 22, 20, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (18, 13, 14, 35, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (19, 13, 25, 20, 0.5, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (20, 14, 15, 30, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (21, 14, 30, 60, 1, 0);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (22, 15, 18, 20, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (23, 15, 17, 60, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (24, 16, 19, 40, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (25, 16, 17, 10, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (26, 17, 35, 60, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (27, 20, 23, 35, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (28, 20, 37, 50, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (29, 21, 22, 40, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (30, 21, 23, 15, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (31, 22, 24, 15, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (32, 23, 24, 40, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (33, 24, 25, 75, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (34, 24, 27, 20, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (35, 25, 26, 23, 0.5, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (36, 26, 29, 22, 0.5, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (37, 27, 28, 20, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (38, 28, 29, 75, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (39, 28, 38, 11, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (40, 29, 41, 44, 0.5, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (41, 29, 30, 35, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (42, 30, 31, 20, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (43, 31, 32, 20, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (44, 32, 33, 20, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (45, 33, 34, 20, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (46, 34, 35, 20, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (47, 35, 36, 23, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (48, 36, 111, 50, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (49, 36, 44, 37, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (50, 37, 38, 70, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (51, 37, 39, 25, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (52, 38, 40, 25, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (53, 39, 45, 50, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (54, 39, 46, 35, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (55, 40, 46, 35, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (56, 40, 48, 50, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (57, 40, 49, 40, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (58, 41, 49, 40, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (59, 41, 42, 17, 0.5, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (60, 42, 43, 30, 1, 0);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (61, 42, 51, 30, 0.5, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (62, 42, 108, 50, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (63, 43, 108, 40, 1, 0);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (64, 43, 51, 30, 1, 0);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (65, 44, 108, 84, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (66, 44, 52, 33, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (67, 45, 47, 35, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (68, 45, 54, 50, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (69, 47, 48, 35, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (70, 48, 50, 40, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (71, 48, 56, 50, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (72, 50, 51, 40, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (73, 51, 59, 35, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (74, 51, 58, 35, 0.5, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (75, 52, 53, 40, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (76, 52, 60, 30, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (77, 53, 61, 100, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (78, 54, 56, 70, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (79, 54, 62, 60, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (80, 55, 57, 70, 1, 0);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (81, 56, 58, 80, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (82, 56, 64, 60, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (83, 57, 74, 63, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (84, 57, 96, 80, 1, 0);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (85, 57, 86, 50, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (86, 58, 59, 35, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (87, 58, 66, 57, 0.5, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (88, 60, 67, 50, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (89, 61, 68, 80, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (90, 62, 63, 35, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (91, 62, 71, 30, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (92, 63, 64, 35, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (93, 64, 65, 40, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (94, 65, 66, 40, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (95, 66, 67, 128, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (96, 66, 75, 32, 0.5, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (97, 67, 78, 36, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (98, 67, 109, 40, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (99, 68, 109, 100, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (100, 68, 70, 13, 1, 0);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (101, 68, 114, 30, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (102, 69, 114, 20, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (103, 69, 76, 70, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (104, 71, 72, 30, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (105, 72, 73, 70, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (106, 72, 112, 60, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (107, 73, 74, 80, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (108, 73, 96, 60, 1, 0);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (109, 74, 75, 30, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (110, 74, 77, 128, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (111, 76, 92, 27, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (112, 77, 78, 30, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (113, 77, 80, 20, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (114, 77, 84, 113, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (115, 78, 79, 40, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (116, 79, 109, 37, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (117, 79, 81, 38, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (118, 80, 81, 30, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (119, 81, 82, 73, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (120, 81, 83, 100, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (121, 83, 84, 50, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (122, 83, 95, 61, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (123, 84, 85, 10, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (124, 85, 86, 110, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (125, 85, 93, 30, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (126, 86, 87, 80, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (127, 86, 91, 30, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (128, 87, 88, 80, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (129, 87, 96, 50, 1, 0);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (130, 88, 112, 50, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (131, 88, 89, 58, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (132, 89, 90, 160, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (133, 90, 91, 30, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (134, 90, 94, 110, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (135, 90, 97, 60, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (136, 92, 105, 270, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (137, 92, 107, 10, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (138, 93, 94, 30, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (139, 94, 95, 60, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (140, 96, 112, 80, 1, 0);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (141, 97, 98, 60, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (142, 99, 113, 420, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (143, 99, 100, 220, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (144, 100, 101, 100, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (145, 101, 102, 100, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (146, 102, 103, 290, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (147, 103, 110, 10, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (148, 103, 104, 120, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (149, 104, 105, 140, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (150, 105, 106, 10, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (151, 106, 107, 270, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (152, 106, 110, 260, 1, 1);
INSERT INTO `edges` (`edge_id`, `src_id`, `dest_id`, `weight`, `crowding`, `is_rideable`) VALUES (153, 112, 113, 20, 1, 1);
COMMIT;

-- ----------------------------
-- Table structure for location_browse_counts
-- ----------------------------
DROP TABLE IF EXISTS `location_browse_counts`;
CREATE TABLE `location_browse_counts` (
  `user_id` int NOT NULL,
  `location_id` int NOT NULL,
  `count` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`user_id`,`location_id`),
  KEY `idx_location_id` (`location_id`),
  CONSTRAINT `location_browse_counts_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`),
  CONSTRAINT `location_browse_counts_ibfk_2` FOREIGN KEY (`location_id`) REFERENCES `locations` (`location_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of location_browse_counts
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for locations
-- ----------------------------
DROP TABLE IF EXISTS `locations`;
CREATE TABLE `locations` (
  `location_id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `type` int NOT NULL,
  `keyword` varchar(255) DEFAULT NULL,
  `popularity` int DEFAULT '0',
  `evaluation` int DEFAULT '0',
  `image_url` varchar(255) DEFAULT '/uploads/images/default_location.jpg',
  `description` text,
  `address` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`location_id`)
) ENGINE=InnoDB AUTO_INCREMENT=202 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of locations
-- ----------------------------
BEGIN;
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (1, '清华大学', 0, '大学', 50, 1, '清华大学.jpg', '中国顶尖学府，理工科实力雄厚，校园环境优美。', '海淀区双清路30号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (2, '北京大学', 0, '大学', 10, 4, '北京大学.jpg', '百年名校，人文底蕴深厚，培养了众多杰出人才。', '海淀区颐和园路5号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (3, '北京航空航天大学', 0, '大学', 34, 2, '北京航空航天大学.jpg', '北京航空航天大学，顶尖理工学府，培养航空航天精英。', '海淀区学院路37号 北京航空航天大学');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (4, '北京理工大学', 0, '大学', 88, 3, '北京理工大学.jpg', '北京理工大学，理工翘楚，创新摇篮，科技报国。', '海淀区北京理工大学(苏州桥地铁站C口步行390米)');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (5, '中国人民大学', 0, '大学', 44, 4, '中国人民大学.jpg', '中国人民大学是一所以人文社科为主的综合性重点大学。', '海淀区北三环西路辅路与中关村大街辅路交叉口西180米');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (6, '北京师范大学', 0, '大学', 23, 5, '北京师范大学.jpg', '百年师范名校，育人为本，底蕴深厚，学科卓越。', '海淀区北京市北太平庄街道新街口外大街19号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (7, '中国农业大学', 0, '大学', 2, 3, '中国农业大学.jpg', '中国农业大学专注农业科技，培养卓越农业人才。', '海淀区清华东路17号 中国农业大学');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (8, '北京科技大学', 0, '大学', 68, 4, '北京科技大学.jpg', '北京科技大学，理工翘楚，创新实践并重，培育卓越科技人才。', '海淀区学院路30号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (9, '北京交通大学', 0, '大学', 46, 2, '北京交通大学.jpg', '北京交通大学以理工见长，培养创新科技人才。', '海淀区红果园路与知行大道交叉口东南20米');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (10, '中国石油大学（北京）', 0, '大学', 72, 0, '中国石油大学（北京）.jpg', '中国石油大学（北京）以理工见长，能源特色鲜明，培养高素质工程', '昌平区府学路18号 中国石油大学（北京）');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (11, '中国矿业大学', 0, '大学', 24, 3, '中国矿业大学.jpg', '中国矿业大学，理工强校，能源特色鲜明，创新实践并重。', '海淀区学院路丁11号中国矿业大学(北京)内');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (12, '北京邮电大学', 0, '大学', 100, 2, '北京邮电大学.jpg', '北京邮电大学，理工强校，信息科技领域的顶尖学府。', '海淀区西土城路10号 北京邮电大学');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (13, '中央财经大学', 0, '大学', 85, 4, '中央财经大学.jpg', '中国财经管理领域顶尖高校，"财经黄埔"美誉。', '海淀区学院南路39号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (14, '北京工业大学', 0, '大学', 36, 4, '北京工业大学.jpg', '北京工业大学以理工见长，培养创新科技人才。', '朝阳区平乐园100号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (15, '北京化工大学', 0, '大学', 56, 5, '北京化工大学.jpg', '北京化工大学是理工强校，化工特色鲜明，科研实力雄厚。', '朝阳区樱花园东街与樱花小街交叉口西南180米');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (16, '中国政法大学', 0, '大学', 84, 4, '中国政法大学.jpg', '中国法学教育最高学府，"五院四系"核心成员。', '昌平区府学路27号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (17, '中国地质大学（北京）', 0, '大学', 23, 2, '中国地质大学（北京）.jpg', '中国地质大学（北京）以理工见长，地质学科优势突出。', '海淀区学院路29号(学院桥地铁站A口步行250米) 中国地质大学（北京）');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (18, '华北电力大学', 0, '大学', 42, 3, '华北电力大学.jpg', '华北电力大学是理工强校，电力特色鲜明，培养能源领域精英。', '昌平区回龙观北农路2号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (19, '对外经济贸易大学', 0, '大学', 78, 4, '对外经济贸易大学.jpg', '中国对外经贸教育的摇篮，国际化特色鲜明。', '朝阳区惠新东街10号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (20, '北京林业大学', 0, '大学', 19, 2, '北京林业大学.jpg', '北京林业大学，绿色学府，专注林业科研与人才培养。', '海淀区清华东路35号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (21, '首都师范大学', 0, '大学', 20, 1, '首都师范大学.jpg', '首都师范大学以师范教育为特色，培养卓越教师人才。', '海淀区西三环北路105号');
-- 公园类景点 (22-37)
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (22, '颐和园', 1, '公园', 92, 5, '颐和园.jpg', '中国现存最完整的皇家园林，融合江南园林与北方山水特色。', '海淀区新建宫门路19号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (23, '天坛公园', 1, '公园', 88, 4, '天坛公园.jpg', '明清帝王祭天场所，以精巧建筑和声学奇迹闻名。', '东城区天坛东里甲1号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (24, '通州大运河森林公园', 1, '公园', 85, 3, '通州大运河森林公园.jpg', '水系景观优美，绿树成荫，休闲好去处。', '通州区宋梁路南段');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (25, '香山公园', 1, '公园', 78, 4, '香山公园.jpg', '北京著名赏枫胜地，秋季红叶景观尤为壮观。', '海淀区买卖街40号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (26, '地坛公园', 1, '公园', 77, 4, '地坛公园.jpg', '明清两朝帝王祭祀"皇地祇神"的场所。', '安定门外大街');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (27, '中山公园', 1, '公园', 75, 4, '中山公园.jpg', '位于天安门西侧，原为社稷坛，现为纪念孙中山先生的公园。', '中华路4号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (28, '景山公园', 1, '公园', 75, 4, '景山公园.jpg', '北京中轴线制高点，可俯瞰故宫全景。', '景山西街44号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (29, '玉渊潭公园', 1, '公园', 47, 4, '玉渊潭公园.jpg', '北京著名公园，春季樱花盛开，景色宜人。', '海淀区西三环中路10号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (30, '什刹海公园', 1, '公园', 33, 3, '什刹海公园.jpg', '北京著名历史文化景区，荷花盛开，古迹众多。', '西城区地安门西大街49号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (31, '青龙湖公园', 1, '公园', 35, 3, '青龙湖公园.jpg', '北京郊区湖泊公园，水域宽广，环境幽静。', '房山区石梯村南街28号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (32, '北宫国家森林公园', 1, '公园', 96, 2, '北宫国家森林公园.jpg', '北京西南郊自然保护区，森林覆盖率高，空气清新。', '丰台区北宫国家森林公园梨园峡谷');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (33, '龙潭中湖公园', 1, '公园', 66, 3, '龙潭中湖公园.jpg', '由北京游乐园改建的城市公园，环境优美。', '左安门内大街19号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (34, '菖蒲河公园', 1, '公园', 66, 3, '菖蒲河公园.jpg', '皇城根遗址公园的一部分，环境优美。', '菖蒲河沿9号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (35, '东单公园', 1, '公园', 65, 3, '东单公园.jpg', '北京市中心的小型公园，休闲健身的好去处。', '崇文门内大街9号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (36, '三里河公园', 1, '公园', 67, 3, '三里河公园.jpg', '前门地区的小型公园，环境清幽。', '中芦草园胡同与草厂三条交叉口西40米');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (37, '世纪公园', 1, '公园', 43, 1, '世纪公园.jpg', '世纪公园绿意盎然，湖光山色，休闲娱乐好去处。', '海淀区玉渊潭南路11号');
-- 博物馆类景点 (38-50)
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (38, '故宫博物院', 1, '博物馆', 98, 5, '故宫博物院.jpg', '明清两代皇家宫殿，世界现存最大最完整的木质结构古建筑群。', '东城区景山前街4号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (39, '中国国家博物馆', 1, '博物馆', 90, 5, '中国国家博物馆.jpg', '中国最高历史文化艺术殿堂，藏品数量达140万件。', '东城区东长安街16号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (40, '中国科学技术馆', 1, '博物馆', 86, 5, '中国科学技术馆.jpg', '国家级科技馆，互动展品丰富，适合科普教育。', '朝阳区北辰东路5号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (41, '国家自然博物馆', 1, '博物馆', 85, 4, '国家自然博物馆.jpg', '中国最大的自然历史博物馆，展示丰富的自然标本。', '东城区天桥南大街126号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (42, '中国美术馆', 1, '博物馆', 82, 4, '中国美术馆.jpg', '国家级美术博物馆，收藏展示中国近现代美术作品。', '东城区五四大街1号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (43, '首都博物馆', 1, '博物馆', 80, 4, '首都博物馆.jpg', '展示北京三千年建城史和八百年建都史的大型博物馆。', '西城区复兴门外大街16号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (44, '中国地质博物馆', 1, '博物馆', 78, 4, '中国地质博物馆.jpg', '亚洲最大的地质学专业博物馆，藏品丰富。', '西城区西四羊肉胡同15号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (45, '北京鲁迅博物馆', 1, '博物馆', 76, 4, '北京鲁迅博物馆.jpg', '展示鲁迅生平与作品的专题博物馆。', '西城区阜城门内宫门口二条19号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (46, '中华民族博物院', 1, '博物馆', 75, 3, '中华民族博物院.jpg', '展示中华民族历史文化的综合性博物馆。', '朝阳区民族园路1号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (47, '北京汽车博物馆', 1, '博物馆', 74, 3, '北京汽车博物馆.jpg', '展示汽车发展历史的专题博物馆，藏品丰富。', '丰台区南四环西路126号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (48, '中国钱币博物馆', 1, '博物馆', 70, 3, '中国钱币博物馆.jpg', '展示中国货币发展历史的专业博物馆。', '西城区西交民巷17号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (49, '中国妇女儿童博物馆', 1, '博物馆', 70, 3, '中国妇女儿童博物馆.jpg', '展示中国妇女儿童发展历史的专题博物馆。', '东城区北极阁路9号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (50, '中国邮政邮票博物馆', 1, '博物馆', 69, 3, '中国邮政邮票博物馆.jpg', '展示中国邮政历史和邮票文化的专业博物馆。', '东城区贡院西街6号');
-- 古建筑类景点 (51-65)
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (51, '八达岭长城', 1, '古建筑', 97, 5, '八达岭长城.jpg', '万里长城最著名的段落，被誉为"天下九塞之一"。', '延庆区G6京藏高速58号出口');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (52, '慕田峪长城', 1, '古建筑', 90, 5, '慕田峪长城.jpg', '万里长城精华段，植被覆盖率高，四季景色各异。', '怀柔区渤海镇慕田峪村');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (53, '居庸关长城', 1, '古建筑', 86, 4, '居庸关长城.jpg', '万里长城重要关隘，以"天下第一雄关"著称。', '昌平区南口镇居庸关村');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (54, '司马台长城', 1, '古建筑', 85, 4, '司马台长城.jpg', '万里长城中唯一保留明代原貌的长城，夜游长城独一无二。', '密云区古北口镇司马台村');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (55, '故宫午门', 1, '古建筑', 83, 4, '故宫午门.jpg', '紫禁城的正门，皇帝举行重要典礼的场所。', '东城区景山前街4号故宫博物院内');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (56, '故宫神武门', 1, '古建筑', 81, 4, '故宫神武门.jpg', '紫禁城的北门，现为故宫博物院正门。', '东城区景山前街4号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (57, '太庙', 1, '古建筑', 80, 4, '太庙.jpg', '明清两代皇帝祭祖的场所，现为劳动人民文化宫。', '东城区东长安街');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (58, '故宫东华门', 1, '古建筑', 79, 3, '故宫东华门.jpg', '紫禁城东门，清代大臣上朝的主要入口。', '东城区景山前街4号故宫博物院');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (59, '正阳门', 1, '古建筑', 76, 3, '正阳门.jpg', '北京内城南垣的正门，老北京城的重要标志性建筑。', '东城区前门大街北端');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (60, '妙应寺白塔', 1, '古建筑', 74, 3, '妙应寺白塔.jpg', '北京现存年代最早、规模最大的喇嘛塔。', '西城区阜成门内大街171号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (61, '钟鼓楼', 1, '古建筑', 74, 3, '钟鼓楼.jpg', '元明清三代都城的报时中心，北京中轴线北端标志。', '东城区钟楼湾胡同');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (62, '钟楼', 1, '古建筑', 73, 3, '钟楼.jpg', '北京中轴线北端的标志性建筑，古代报时中心。', '东城区钟楼湾');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (63, '先农坛', 1, '古建筑', 70, 3, '先农坛.jpg', '明清两代皇帝祭祀先农和举行亲耕典礼的场所。', '西城区东经路21号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (64, '北京大观园', 1, '古建筑', 14, 3, '北京大观园.jpg', '以《红楼梦》为主题的文化园，古典园林风格。', '西城区南菜园街12号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (65, '古北水镇', 1, '古建筑', 35, 1, '古北水镇.jpg', '北京郊区水乡古镇，夜景迷人，长城脚下好去处。', '密云区古北口镇司马台村');
-- 寺庙类景点 (66-75)
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (66, '雍和宫', 1, '寺庙', 80, 4, '雍和宫.jpg', '北京最大的藏传佛教寺院，曾是雍正皇帝府邸。', '东城区雍和宫大街28号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (67, '潭柘寺', 1, '寺庙', 77, 4, '潭柘寺.jpg', '北京最古老的佛教寺庙，有"先有潭柘寺，后有北京城"之说。', '门头沟区潭柘寺镇');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (68, '红螺寺', 1, '寺庙', 79, 4, '红螺寺.jpg', '北方著名佛教寺院，环境优美。', '怀柔区红螺东路2号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (69, '戒台寺', 1, '寺庙', 76, 4, '戒台寺.jpg', '以戒坛闻名的佛教古寺，环境清幽。', '门头沟区永定镇戒台寺');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (70, '东岳庙', 1, '寺庙', 75, 3, '东岳庙.jpg', '道教正一派在华北地区最大的庙宇。', '朝阳区朝阳门外大街141号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (71, '大觉寺', 1, '寺庙', 74, 3, '大觉寺.jpg', '千年古刹，以古树名木和清泉闻名。', '海淀区苏家坨镇大觉寺路9号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (72, '白云观', 1, '寺庙', 73, 3, '白云观.jpg', '道教全真派祖庭，北京最大的道教宫观。', '西城区白云观街7号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (73, '法海寺', 1, '寺庙', 73, 3, '法海寺.jpg', '以明代壁画闻名的佛教寺庙。', '石景山区模式口大街48号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (74, '云居寺', 1, '寺庙', 72, 3, '云居寺.jpg', '以石刻佛经闻名于世的佛教寺院。', '房山区大石窝镇水头村');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (75, '法源寺', 1, '寺庙', 72, 3, '法源寺.jpg', '北京最古老的名刹之一，中国佛学院所在地。', '西城区法源寺前街7号');
-- 动物园类景点 (76-80)
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (76, '北京动物园', 1, '动物园', 85, 4, '北京动物园.jpg', '中国开放最早、动物种类最多的动物园，大熊猫馆是亮点。', '西直门外大街137号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (77, '北京野生动物园', 1, '动物园', 77, 3, '北京野生动物园.jpg', '自然生态环境优美，近距离观赏野生动物的乐园。', '大兴区榆垡镇万亩森林内');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (78, '北京海洋馆', 1, '动物园', 82, 4, '北京海洋馆.jpg', '亚洲最大的内陆水族馆，展示千余种海洋生物。', '海淀区高梁桥斜街乙18号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (79, '北京植物园', 1, '植物园', 72, 4, '北京植物园.jpg', '集科研、科普、游览功能于一体的综合性植物园。', '香山卧佛寺路');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (80, '国家植物园北园', 1, '植物园', 48, 4, '国家植物园北园.jpg', '植物种类繁多，环境优美，科研价值高。', '海淀区卧佛寺路');
-- 游山类景点 (81-90)
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (81, '凤凰岭', 1, '游山', 85, 4, '凤凰岭.jpg', '"京西小黄山"，奇峰怪石林立，自然景观与佛教文化交融。', '海淀区苏家坨镇凤凰岭路19号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (82, '雾灵山', 1, '游山', 83, 4, '雾灵山.jpg', '燕山主峰，国家级自然保护区，夏季避暑胜地。', '密云区新城子镇');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (83, '妙峰山', 1, '游山', 82, 4, '妙峰山.jpg', '京西宗教名山，玫瑰谷和古刹闻名，徒步爱好者的天堂。', '门头沟区妙峰山镇');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (84, '云蒙山', 1, '游山', 80, 4, '云蒙山.jpg', '"北方小黄山"，奇松怪石云海堪称三绝，四季景色壮美。', '密云区石城镇');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (85, '百望山', 1, '游山', 78, 3, '百望山.jpg', '距市区最近的国家森林公园，登顶可俯瞰北京城全景。', '海淀区黑山扈北口19号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (86, '百花山', 1, '游山', 76, 3, '百花山.jpg', '华北植物基因库，高山草甸景观独特，四季野花不断。', '门头沟区清水镇');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (87, '圣莲山', 1, '游山', 75, 3, '圣莲山.jpg', '"京西小五岳"，道教文化名山，二十八盘道险峻奇特。', '房山区史家营乡');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (88, '鹫峰', 1, '游山', 74, 3, '鹫峰.jpg', '北京林业大学实验林场，登山步道完善，红叶景观优美。', '海淀区苏家坨镇');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (89, '阳台山', 1, '游山', 72, 3, '阳台山.jpg', '京西古香道穿越地，登顶可眺望京城，春季山桃遍野。', '海淀区与门头沟区交界');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (90, '十三陵水库', 1, '游山', 75, 3, '十三陵水库.jpg', '北京著名水库，环境优美，可垂钓游船，周边景色宜人。', '昌平区十三陵镇');
-- 玩水类景点 (91-100)
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (91, '昆明湖', 1, '玩水', 92, 5, '昆明湖.jpg', '皇家园林最大湖泊，可泛舟赏景，夏季荷花盛开美不胜收。', '海淀区新建宫门路19号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (92, '北海', 1, '玩水', 90, 4, '北海.jpg', '"让我们荡起双桨"诞生地，可划船欣赏白塔倒影。', '西城区文津街1号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (93, '什刹海水域', 1, '玩水', 88, 4, '什刹海水域.jpg', '北京城内最大天然水域，夏夜酒吧街与游船相映成趣。', '西城区地安门西大街');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (94, '龙庆峡', 1, '玩水', 87, 4, '龙庆峡.jpg', '"北方小漓江"，乘船穿梭峡谷，冬季冰灯节更是一绝。', '延庆区古城村');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (95, '雁栖湖', 1, '玩水', 85, 4, '雁栖湖.jpg', 'APEC会址所在地，湖光山色相映，水上娱乐设施完善。', '怀柔区雁水路3号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (96, '十渡拒马河', 1, '玩水', 84, 4, '拒马河.jpg', '北方最大喀斯特水景，漂流、竹筏等亲水项目丰富。', '房山区十渡镇');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (97, '金海湖', 1, '玩水', 82, 4, '金海湖.jpg', '北京最大综合水上娱乐区，可体验游艇、蹦极等刺激项目。', '平谷区金海湖镇');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (98, '青龙湖', 1, '玩水', 80, 3, '青龙湖.jpg', '京西最大生态水域，可体验皮划艇、帆板等水上运动。', '丰台区王佐镇');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (99, '野鸭湖', 1, '玩水', 78, 3, '野鸭湖.jpg', '北京最大湿地公园，观鸟胜地，环湖骑行道风景优美。', '延庆区康野路');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (100, '温榆河公园', 1, '玩水', 76, 3, '温榆河.jpg', '北京最大"绿肺"，水系纵横，可体验桨板等新兴水上运动。', '朝阳区来广营北路');
-- 商业街区（101-110）
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (101, '王府井步行街', 1, '商业街区', 95, 4, '王府井步行街.jpg', '百年金街，汇聚老字号与奢侈品旗舰店，北京商业地标。', '东城区王府井大街');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (102, '前门大街', 1, '商业街区', 90, 4, '前门大街.jpg', '明清御道改造的步行街，全聚德等老字号云集。', '东城区前门东大街');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (103, '国贸商城', 1, '商业街区', 88, 4, '国贸商城.jpg', 'CBD核心高端商场，国际大牌旗舰店聚集地。', '朝阳区建国门外大街1号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (104, '蓝色港湾', 1, '商业街区', 85, 4, '蓝色港湾.jpg', '欧式风情购物公园，夜景灯光秀闻名。', '朝阳区朝阳公园路6号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (105, '华熙LIVE·五棵松', 1, '商业街区', 83, 4, '华熙LIVE.jpg', '潮流运动主题商业区，演唱会与赛事聚集地。', '海淀区复兴路69号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (106, '侨福芳草地', 1, '商业街区', 82, 4, '芳草地.jpg', '艺术购物中心，建筑获国际设计大奖。', '朝阳区东大桥路9号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (107, '合生汇', 1, '商业街区', 80, 3, '合生汇.jpg', '年轻潮流聚集地，深夜食堂特色突出。', '朝阳区西大望路21号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (108, '中关村食宝街', 1, '商业街区', 78, 3, '食宝街.jpg', '科技园区美食地标，网红餐饮品牌试验场。', '海淀区中关村大街15号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (109, '隆福寺文创园', 1, '商业街区', 76, 3, '隆福寺.jpg', '老庙改造的文艺街区，定期举办市集展览。', '东城区隆福寺街95号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (110, '簋街', 1, '商业街区', 85, 3, '簋街.jpg', '24小时餐饮街，小龙虾和火锅聚集地。', '东城区东直门内大街');
-- 胡同文化（111-120）
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (111, '南锣鼓巷', 1, '胡同文化', 90, 4, '南锣鼓巷.jpg', '北京最著名胡同，文艺小店与四合院民宿云集。', '东城区南锣鼓巷');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (112, '国子监街', 1, '胡同文化', 82, 4, '国子监街.jpg', '元明清最高学府所在，槐荫古道文化气息浓厚。', '东城区国子监街');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (113, '帽儿胡同', 1, '胡同文化', 78, 3, '帽儿胡同.jpg', '保存完好的四合院群落，婉容故居所在地。', '东城区帽儿胡同');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (114, '琉璃厂', 1, '胡同文化', 80, 3, '琉璃厂.jpg', '古籍文玩字画一条街，传统文化体验地。', '西城区琉璃厂东街');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (115, '八大胡同', 1, '胡同文化', 75, 3, '八大胡同.jpg', '清末民初风月场所旧址，青砖灰瓦历史印记。', '西城区珠市口西大街');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (116, '史家胡同', 1, '胡同文化', 76, 3, '史家胡同.jpg', '名人故居扎堆，胡同博物馆记录老北京生活。', '东城区史家胡同');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (117, '百花深处', 1, '胡同文化', 72, 3, '百花深处.jpg', '名字诗意的窄胡同，摇滚音乐文化发源地之一。', '西城区新街口南大街');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (118, '方家胡同', 1, '胡同文化', 74, 3, '方家胡同.jpg', '创意工厂与老北京生活交融的文艺胡同。', '东城区方家胡同');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (119, '砖塔胡同', 1, '胡同文化', 70, 3, '砖塔胡同.jpg', '北京最古老胡同，元代万松老人塔矗立其中。', '西城区西四南大街');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (120, '西总布胡同', 1, '胡同文化', 68, 3, '西总布胡同.jpg', '网红打卡地，现代建筑与胡同风貌碰撞。', '东城区西总布胡同');
-- 展览馆（121-130）
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (121, '中国国际展览中心（新馆）', 1, '展览馆', 85, 4, '国展新馆.jpg', '亚洲最大现代化展馆，承办国际车展等大型展会。', '顺义区天竺地区裕翔路88号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (122, '北京展览馆剧场', 1, '展览馆', 80, 4, '北展剧场.jpg', '俄式建筑风格，北京国际电影节主会场。', '西城区西直门外大街135号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (123, '国家会议中心', 1, '展览馆', 88, 4, '国家会议中心.jpg', 'APEC等国际峰会举办地，配套首都会展新馆。', '朝阳区天辰东路7号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (124, '中国科技会展中心', 1, '展览馆', 78, 3, '科技会展中心.jpg', '中关村科技主题展会聚集地。', '海淀区西外大街135号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (125, '北京农业展览馆', 1, '展览馆', 76, 3, '农业展览馆.jpg', '苏式建筑群，常年举办农业主题展览。', '朝阳区东三环北路16号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (126, '中华世纪坛世界艺术馆', 1, '展览馆', 82, 4, '世纪坛.jpg', '旋转坛体建筑，举办世界文明主题特展。', '海淀区复兴路甲9号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (127, '中国国际科技会展中心', 1, '展览馆', 75, 3, '科技会展.jpg', '承办科技产业博览会等专业展会。', '朝阳区北三环东路6号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (128, '北京民族文化宫展览馆', 1, '展览馆', 74, 3, '民族文化宫.jpg', '民族主题常设展，建筑列入北京十大建筑。', '西城区复兴门内大街49号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (129, '中国国际展览中心（静安庄馆）', 1, '展览馆', 80, 3, '国展老馆.jpg', '北京最早专业展馆，现主要承办中小型展会。', '朝阳区北三环东路6号');
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES (130, '北京规划展览馆', 1, '展览馆', 78, 3, '规划展览馆.jpg', '巨型北京沙盘模型展示城市发展规划。', '东城区前门东大街20号');
-- 科技馆类（131-137）
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES
(131, '北京科学中心', 1, '科技馆', 85, 4, '北京科学中心.jpg', '北京科技周主会场，三生主题展馆适合亲子体验。', '西城区北辰路9号'),
(132, '中国铁道博物馆东郊馆', 1, '科技馆', 82, 4, '铁道博物馆东郊馆.jpg', '展示百年铁路史，可登入真实蒸汽机车内部参观。', '朝阳区酒仙桥北路1号院'),
(133, '中国古动物馆', 1, '科技馆', 80, 3, '中国古动物馆.jpg', '展示5亿年脊椎动物演化史，恐龙化石最全。', '西城区西直门外大街142号'),
(134, '中国航空博物馆', 1, '科技馆', 86, 4, '中国航空博物馆.jpg', '亚洲最大航空珍品荟萃地，200+实体飞机展示。', '昌平区小汤山镇'),
(135, '索尼探梦科技馆', 1, '科技馆', 78, 3, '索尼探梦科技馆.jpg', '光影科技互动体验，青少年科普实践基地。', '朝阳区朝阳公园内'),
(136, '国家纳米科学中心', 1, '科技馆', 75, 3, '国家纳米科学中心.jpg', '纳米科技专题展，可视化分子结构模型。', '海淀区中关村北一条11号'),
(137, '中国消防博物馆', 1, '科技馆', 76, 3, '中国消防博物馆.jpg', '沉浸式消防体验，模拟火场逃生训练。', '西城区广安门南街70号');
-- 美术馆类（138-146）
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES
(138, '中央美术学院美术馆', 1, '美术馆', 88, 4, '央美美术馆.jpg', '先锋艺术实验场，建筑获评全球十佳美术馆。', '朝阳区花家地南街8号'),
(139, 'UCCA尤伦斯当代艺术中心', 1, '美术馆', 90, 4, 'UCCA尤伦斯.jpg', '国际级当代艺术展馆，798艺术区地标。', '朝阳区798艺术区4号路'),
(140, '今日美术馆', 1, '美术馆', 85, 4, '今日美术馆.jpg', '中国首家民营非盈利美术馆，前卫艺术阵地。', '朝阳区百子湾路32号'),
(141, '红砖美术馆', 1, '美术馆', 87, 4, '红砖美术馆.jpg', '红砖建筑与当代艺术完美融合，网红打卡地。', '朝阳区崔各庄乡何各庄村'),
(142, '北京画院美术馆', 1, '美术馆', 80, 3, '北京画院美术馆.jpg', '齐白石作品常设展，传统书画研究重镇。', '朝阳区朝阳公园南路12号'),
(143, '清华大学艺术博物馆', 1, '美术馆', 86, 4, '清华艺博.jpg', '高校顶级艺术馆，常设织绣、陶瓷等专题展。', '海淀区清华大学校内'),
(144, '木木美术馆', 1, '美术馆', 83, 3, '木木美术馆.jpg', '年轻艺术家孵化平台，跨界艺术项目丰富。', '东城区隆福寺街95号'),
(145, '松美术馆', 1, '美术馆', 84, 4, '松美术馆.jpg', '199棵松树环绕的极简主义艺术空间。', '顺义区天竺镇楼台村'),
(146, '嘉德艺术中心', 1, '美术馆', 82, 4, '嘉德艺术中心.jpg', '拍卖行旗下艺术空间，文物级展品频现。', '东城区王府井大街1号');
-- 红色景区（147-154）
INSERT INTO `locations` (`location_id`, `name`, `type`, `keyword`, `popularity`, `evaluation`, `image_url`, `description`, `address`) VALUES
(147, '天安门广场', 1, '红色景区', 99, 5, '天安门广场.jpg', '世界最大城市广场，见证新中国重大历史时刻。', '东城区东长安街'),
(148, '中国人民革命军事博物馆', 1, '红色景区', 92, 5, '军事博物馆.jpg', '馆藏18万件军事文物，包括东风导弹等镇馆之宝。', '海淀区复兴路9号'),
(149, '中国人民抗日战争纪念馆', 1, '红色景区', 88, 4, '抗战纪念馆.jpg', '全面展示14年抗战史，卢沟桥事变原发地。', '丰台区卢沟桥宛平城内'),
(150, '新文化运动纪念馆', 1, '红色景区', 86, 4, '新文化运动纪念馆.jpg', '五四运动策源地，北大红楼历史原址。', '东城区五四大街29号'),
(151, '香山革命纪念地', 1, '红色景区', 85, 4, '香山革命纪念地.jpg', '中共中央"进京赶考"第一站，毛泽东旧居保存完好。', '海淀区香山公园内'),
(152, '李大钊故居', 1, '红色景区', 84, 4, '李大钊故居.jpg', '中国共产党主要创始人故居，重要革命遗址。', '西城区文华胡同24号'),
(153, '宋庆龄故居', 1, '红色景区', 83, 4, '宋庆龄故居.jpg', '国家名誉主席最后居所，展示生平事迹。', '西城区后海北沿46号'),
(154, '八宝山革命公墓', 1, '红色景区', 80, 3, '八宝山革命公墓.jpg', '安葬党和国家领导人及革命烈士的圣地。', '石景山区石景山路9号');

COMMIT;

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `user_id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) DEFAULT NULL,
  `avatar` varchar(255) DEFAULT 'default_avatar.jpg',
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `email_UNIQUE` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of users
-- ----------------------------
BEGIN;
INSERT INTO `users` (`user_id`, `username`, `email`, `password`, `avatar`) VALUES (1, 'admin', '<EMAIL>', '123456', 'default_avatar.jpg');
COMMIT;

-- ----------------------------
-- Table structure for article_favorites
-- ----------------------------
DROP TABLE IF EXISTS `article_favorites`;
CREATE TABLE `article_favorites` (
  `user_id` int NOT NULL,
  `article_id` int NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_id`,`article_id`),
  KEY `article_id` (`article_id`),
  CONSTRAINT `article_favorites_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`),
  CONSTRAINT `article_favorites_ibfk_2` FOREIGN KEY (`article_id`) REFERENCES `articles` (`article_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of article_favorites
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for vertexes
-- ----------------------------
DROP TABLE IF EXISTS `vertexes`;
CREATE TABLE `vertexes` (
  `vertex_id` int NOT NULL,
  `label` varchar(255) DEFAULT NULL,
  `x` int NOT NULL,
  `y` int NOT NULL,
  `type` int NOT NULL,
  PRIMARY KEY (`vertex_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of vertexes
-- ----------------------------
BEGIN;
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (1, 5, 750, '北邮科技大厦', 4);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (2, 175, 760, '北门', 1);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (3, 92, 745, '北门外卖柜', 5);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (4, 176, 750, '邮驿站', 5);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (5, 28, 707, '北邮科技大厦东门', 4);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (6, 50, 707, '学十一公寓', 8);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (7, 90, 707, '学十西侧路口', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (8, 135, 708, '学十公寓', 8);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (9, 178, 709, '学十东侧路口', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (10, 280, 730, '学六洗衣房', 5);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (11, 325, 720, '学六公寓', 8);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (12, 92, 690, '教九楼', 3);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (13, 180, 690, '经管楼西南路口', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (14, 220, 692, '经管楼', 3);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (15, 260, 694, '大创中心', 4);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (16, 326, 707, '学六非机动车停车场', 6);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (17, 326, 697, '学六东南路口', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (18, 260, 670, '学六南侧停车场', 6);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (19, 360, 707, '科研楼', 4);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (20, 14, 659, '学九西南路口', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (21, 55, 678, '学九公寓', 8);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (22, 94, 678, '学九东南路口', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (23, 55, 661, '留学生公寓', 8);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (24, 95, 666, '外训楼', 3);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (25, 181, 670, '学生活动中心', 4);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (26, 183, 650, '综合食堂东门', 7);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (27, 97, 650, '综合食堂西门', 7);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (28, 97, 625, '外训楼东南路口', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (29, 184, 631, '综合食堂东南路口', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (30, 223, 632, '麦当劳', 7);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (31, 235, 633, '浴室', 5);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (32, 245, 633, '物美超市', 5);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (33, 260, 634, '瑞幸咖啡', 7);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (34, 280, 634, '墨元庄打印店', 5);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (35, 331, 636, '水果店', 5);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (36, 333, 612, '学苑风味餐厅', 7);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (37, 15, 610, '学五西北路口', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (38, 96, 613, '学五东北路口', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (39, 15, 590, '学五西南路口', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (40, 97, 590, '学五东南路口', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (41, 185, 590, '牛顿像', 10);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (42, 186, 573, '大电视', 10);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (43, 210, 558, '小松林', 10);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (44, 334, 577, '学生食堂', 7);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (45, 17, 540, '学十三公寓', 8);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (46, 60, 590, '学五公寓', 8);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (47, 60, 540, '学三公寓', 8);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (48, 97, 540, '学三东北路口', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (49, 145, 590, '学八公寓', 8);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (50, 145, 540, '学四公寓', 8);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (51, 189, 540, '图书馆西北路口', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (52, 336, 546, '学生食堂西南路口', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (53, 380, 546, '学生食堂南门', 7);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (54, 18, 510, '学三西南路口', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (55, 272, 345, '主楼', 3);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (56, 98, 510, '学三东南路口', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (57, 196, 345, '毛主席像', 10);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (58, 190, 507, '图书馆西南路口', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (59, 225, 522, '图书馆', 4);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (60, 336, 510, '篮球场', 9);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (61, 495, 550, '网球场东北路口', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (62, 22, 457, '北邮人之家', 5);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (63, 60, 457, '学一公寓', 8);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (64, 100, 458, '学一东南路口', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (65, 140, 460, '学二公寓', 8);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (66, 191, 461, '学二东南路口', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (67, 337, 469, '图书馆东侧停车场', 6);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (68, 496, 475, '网球场东南路口', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (69, 550, 477, '东门', 1);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (70, 500, 462, '游泳馆', 9);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (71, 24, 420, '教四楼西门', 3);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (72, 24, 400, '西门邮局', 5);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (73, 103, 403, '教四楼', 3);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (74, 195, 405, '奉献走廊', 10);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (75, 194, 425, '教四楼东门/教一楼西门', 3);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (76, 565, 478, '东门外路口', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (77, 337, 401, '教一东南路口', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (78, 337, 430, '教一楼东门', 3);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (79, 387, 433, '体育馆', 9);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (80, 370, 395, '科学会堂', 4);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (81, 397, 393, '科学会堂东北路口', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (82, 397, 475, '田径场', 9);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (83, 399, 297, '李白烈士像', 10);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (84, 341, 297, '科学会堂南门', 4);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (85, 330, 296, '教二东北路口', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (86, 200, 292, '教三非机动车停车场', 6);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (87, 110, 292, '教三楼', 3);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (88, 27, 290, '西门停车场', 6);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (89, 30, 225, '校车停车场', 6);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (90, 201, 229, '南门邮局', 5);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (91, 200, 255, '教三楼东门/教二楼西门', 3);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (92, 565, 506, '东门外红绿灯', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (93, 330, 270, '教二楼东门', 3);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (94, 330, 234, '教二东南路口', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (95, 399, 236, '创新楼', 4);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (96, 106, 343, '校训石', 10);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (97, 204, 190, '南门', 1);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (98, 170, 187, '校医院', 5);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (99, -30, 750, '师大北路西口', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (100, -38, 980, '蓟门桥地铁站', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (101, 165, 1050, '蓟门桥东路口', 11);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (102, 285, 1050, '蓟门桥东公交站', 11);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (103, 540, 1040, '杏坛路北口西侧', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (104, 548, 900, '金谷园', 7);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (105, 553, 775, '师大北路东口', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (106, 563, 775, '邮电北路西口', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (107, 576, 506, '北师大西门', 1);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (108, 250, 575, '学生发展中心', 4);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (109, 387, 470, '体育馆停车场', 6);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (110, 550, 1040, '杏坛路北口东侧', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (111, 370, 612, '科研楼车库', 6);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (112, 26, 339, '西门', 1);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (113, -12, 337, '西门外路口', 2);
INSERT INTO `vertexes` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (114, 525, 477, '学二十九公寓', 8);
COMMIT;

-- ----------------------------
-- Table structure for location_browse_history
-- ----------------------------
DROP TABLE IF EXISTS `location_browse_history`;
CREATE TABLE `location_browse_history` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `location_id` int NOT NULL,
  `browse_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `location_id` (`location_id`),
  CONSTRAINT `location_browse_history_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`),
  CONSTRAINT `location_browse_history_ibfk_2` FOREIGN KEY (`location_id`) REFERENCES `locations` (`location_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of location_browse_history
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for location_favorites
-- ----------------------------
DROP TABLE IF EXISTS `location_favorites`;
CREATE TABLE `location_favorites` (
  `user_id` INT NOT NULL,
  `location_id` INT NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (user_id, location_id),
  FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
  FOREIGN KEY (location_id) REFERENCES locations(location_id) ON DELETE CASCADE
);

-- 创建索引以提高查询性能
CREATE INDEX idx_location_favorites_user_id ON location_favorites(user_id);
CREATE INDEX idx_location_favorites_location_id ON location_favorites(location_id);

-- ----------------------------
-- Records of location_favorites
-- ----------------------------
BEGIN;
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
-- ----------------------------
-- Table structure for restaurant
-- ----------------------------
DROP TABLE IF EXISTS `restaurant`;
CREATE TABLE `restaurant` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `cuisine_type` varchar(255) DEFAULT NULL,
  `popularity` int(11) DEFAULT NULL,
  `evaluation` float DEFAULT NULL,
  `number_of_view` int(11) DEFAULT NULL,
  `dishes_name(price)` varchar(255) DEFAULT NULL,
  `dishes_name1(price)` varchar(255) DEFAULT NULL,
  `dishes_name2(price)` varchar(255) DEFAULT NULL,
  `dishes_image` varchar(255) DEFAULT NULL,
  `dishes_image1` varchar(255) DEFAULT NULL,
  `dishes_image2` varchar(255) DEFAULT NULL,
  `average_price_perperson` float DEFAULT NULL,
  `x` int(11) DEFAULT NULL,
  `y` int(11) DEFAULT NULL,
  `image_url` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=61 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Records of restaurant
-- ----------------------------
BEGIN;
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (1, '新和小馆', '北京菜', 2967, 4.3, 9622, '烤鸭（158）', '金汤酸菜鱼（88）', '京酱肉丝（42）', '/uploads/dishes/烤鸭.jpg', '/uploads/dishes/金汤酸菜鱼.jpg', '/uploads/dishes/京酱肉丝.jpg', 65, 500, 950, '/uploads/restaurants/新和小馆.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (2, '布拉格餐厅', '西餐', 1872, 4.3, 13196, '布拉格精选混合肉盘（188）', '意大利肉酱面（49）', '欧式黑松露奶油蘑菇浓汤（49）', '/uploads/dishes/布拉格精选混合肉盘.jpg', '/uploads/dishes/意大利肉酱面.jpg', '/uploads/dishes/欧式黑松露奶油蘑菇浓汤.jpg', 143, 30, 700, '/uploads/restaurants/布拉格餐厅.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (3, '玉林烤鸭店', '广西菜', 4510, 4.4, 13528, '王牌烤鸭（188）', '贝勒爷烤羊肉（88）', '广西特色干烧鱼（138）', '/uploads/dishes/王牌烤鸭.jpg', '/uploads/dishes/贝勒爷烤羊肉.jpg', '/uploads/dishes/广西特色干烧鱼.jpg', 96, 420, -430, '/uploads/restaurants/玉林烤鸭店.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (4, '牛排家', '西餐', 8078, 4.8, 17858, '经典极佳级牛小排（298）', '精品惠灵顿（698）', '果木炙烤极佳级牛小排（278）', '/uploads/dishes/经典极佳级牛小排.jpg', '/uploads/dishes/精品惠灵顿.jpg', '/uploads/dishes/果木炙烤极佳级牛小排.jpg', 238, 130, -640, '/uploads/restaurants/牛排家.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (5, '悦融琥珀', '京鲁菜', 2247, 4.8, 11186, '大匠葱烧鱼头（398）', '悦融酥香嫩烤鸭（288）', '致味葱烧海参（188）', '/uploads/dishes/大匠葱烧鱼头.jpg', '/uploads/dishes/悦融酥香嫩烤鸭.jpg', '/uploads/dishes/致味葱烧海参.jpg', 165, 1340, 1160, '/uploads/restaurants/悦融琥珀.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (6, '庆和楼', '北京菜', 7660, 4.6, 16450, '贝勒爷烤肉（98）', '京城小酥肉（96）', '松鼠桂鱼（128）', '/uploads/dishes/贝勒爷烤肉.jpg', '/uploads/dishes/京城小酥肉.jpg', '/uploads/dishes/松鼠桂鱼.jpg', 126, 1540, 40, '/uploads/restaurants/庆和楼.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (7, '聚宝源', '北京菜', 13804, 4.4, 59000, '手切鲜羊肉（46）', '手切鲜羊上脑（52）', '鲜牛百叶（48）', '/uploads/dishes/手切鲜羊肉.jpg', '/uploads/dishes/手切鲜羊上脑.jpg', '/uploads/dishes/鲜牛百叶.jpg', 120, 2110, 1520, '/uploads/restaurants/聚宝源.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (8, '佳木斯人在北京', '东北菜', 14337, 4.4, 39102, '佳木斯特色拌菜（35）', '羊肉串（27.7）', '特色大冷面（19.8）', '/uploads/dishes/佳木斯特色拌菜.jpg', '/uploads/dishes/羊肉串.jpg', '/uploads/dishes/特色大冷面.jpg', 77, 2110, 3000, '/uploads/restaurants/佳木斯人在北京.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (9, '广州顺德菜馆', '粤菜', 1173, 3.9, 1901, '三杯鸡（58）', '港式烧鹅（78）', '顺德特色桑拿鸡（128）', '/uploads/dishes/三杯鸡.jpg', '/uploads/dishes/港式烧鹅.jpg', '/uploads/dishes/顺德特色桑拿鸡.jpg', 75, 1200, 2000, '/uploads/restaurants/广州顺德菜馆.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (10, '居桢会缘海鲜酒楼', '潮州菜', 2607, 4.5, 5620, '极品毛血旺（189）', '小炒黄牛肉（68）', '大汉奖牛肉（89）', '/uploads/dishes/极品毛血旺.jpg', '/uploads/dishes/小炒黄牛肉.jpg', '/uploads/dishes/大汉奖牛肉.jpg', 97, 2400, 6400, '/uploads/restaurants/居桢会缘海鲜酒楼.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (11, '徽京小镇', '徽菜', 1196, 4.7, 2612, '徽州臭鳜鱼（188）', '水芹炒肉丝（68）', '红门山羊肉（168）', '/uploads/dishes/徽州臭鳜鱼.jpg', '/uploads/dishes/水芹炒肉丝.jpg', '/uploads/dishes/红门山羊肉.jpg', 133, 448, 915, '/uploads/restaurants/徽京小镇.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (12, '锦府酒楼', '粤菜', 320, 4.5, 791, '鲜笋煮腊排（88）', '春笋炒腊肉（88）', '香椿拌马蹄（78）', '/uploads/dishes/鲜笋煮腊排.jpg', '/uploads/dishes/春笋炒腊肉.jpg', '/uploads/dishes/香椿拌马蹄.jpg', 112, 60, 700, '/uploads/restaurants/锦府酒楼.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (13, '柿子树烤羊腿', '新疆菜', 4652, 4.4, 23318, '招牌烤羊腿（296）', '秘制烤羊排（185）', '烤大虾（10）', '/uploads/dishes/招牌烤羊腿.jpg', '/uploads/dishes/秘制烤羊排.jpg', '/uploads/dishes/烤大虾.jpg', 100, 10, -128, '/uploads/restaurants/柿子树烤羊腿.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (14, '北影主题餐厅', '江西菜', 2791, 4.5, 5505, '江西老表鸭（188）', '招牌三杯鸡（188）', '小炒黄牛肉（78）', '/uploads/dishes/江西老表鸭.jpg', '/uploads/dishes/招牌三杯鸡.jpg', '/uploads/dishes/小炒黄牛肉.jpg', 88, 50, 1600, '/uploads/restaurants/北影主题餐厅.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (15, '清真祥和斋餐厅', '新疆菜', 250, 3.9, 3277, '大盘鸡（49.9）', '铜锅涮肉（30）', '手扒羊肉（58）', '/uploads/dishes/大盘鸡.jpg', '/uploads/dishes/铜锅涮肉.jpg', '/uploads/dishes/手扒羊肉.jpg', 69, -100, 1300, '/uploads/restaurants/清真祥和斋餐厅.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (16, '沸炉重庆老火锅', '重庆菜', 12362, 4.5, 29658, '金牌毛肚（50）', '招牌鸭血（22）', '精选肥牛（39）', '/uploads/dishes/金牌毛肚.jpg', '/uploads/dishes/招牌鸭血.jpg', '/uploads/dishes/精选肥牛.jpg', 95, 1000, 2000, '/uploads/restaurants/沸炉重庆老火锅.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (17, '味象餐厅', '北京菜', 85, 3.6, 151, '咖喱牛肉（35）', '私房牛肉面（26）', '麻辣豆腐（22）', '/uploads/dishes/咖喱牛肉.jpg', '/uploads/dishes/私房牛肉面.jpg', '/uploads/dishes/麻辣豆腐.jpg', 28, 350, 830, '/uploads/restaurants/味象餐厅.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (18, '郭郭的小院', '东北菜', 2350, 4.3, 7428, '铁锅炖鱼（158）', '铁锅炖大鹅（288）', '东北锅包肉（68）', '/uploads/dishes/铁锅炖鱼.jpg', '/uploads/dishes/铁锅炖大鹅.jpg', '/uploads/dishes/东北锅包肉.jpg', 89, 275, -230, '/uploads/restaurants/郭郭的小院.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (19, '楼上楼茶餐厅', '粤菜', 2641, 4.2, 5888, '北邮凉面（9）', '杏鲍菇三杯鸡（33）', '面包心语（26）', '/uploads/dishes/北邮凉面.jpg', '/uploads/dishes/杏鲍菇三杯鸡.jpg', '/uploads/dishes/面包心语.jpg', 51, 200, 600, '/uploads/restaurants/楼上楼茶餐厅.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (20, '同心园饭庄', '北京菜', 12017, 4.3, 38382, '松鼠桂鱼（168）', '八大碗之炖牛肉（88）', '酱羊头肉（58）', '/uploads/dishes/松鼠桂鱼.jpg', '/uploads/dishes/八大碗之炖牛肉.jpg', '/uploads/dishes/酱羊头肉.jpg', 152, 1300, 200, '/uploads/restaurants/同心园饭庄.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (21, '许家菜', '川菜', 4315, 4.6, 11721, '许家飘香牛水煮（156）', '宫保牡丹大虾球（98）', '毛血旺（298）', '/uploads/dishes/许家飘香牛水煮.jpg', '/uploads/dishes/宫保牡丹大虾球.jpg', '/uploads/dishes/毛血旺.jpg', 200, 6000, 4000, '/uploads/restaurants/许家菜.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (22, '巴依老爷新疆美食', '新疆菜', 10429, 4.4, 10236, '馕坑黄金烤肉（136）', '大盘鸡（69）', '老爷家的抓饭（35）', '/uploads/dishes/馕坑黄金烤肉.jpg', '/uploads/dishes/大盘鸡.jpg', '/uploads/dishes/老爷家的抓饭.jpg', 79, 730, 0, '/uploads/restaurants/巴依老爷新疆美食.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (23, '辉哥火锅', '粤菜', 2789, 4.8, 20637, '海鲜拼盘（228）', '招牌黄金花椒鸡汤（398）', '招牌雪花牛肉（389）', '/uploads/dishes/海鲜拼盘.jpg', '/uploads/dishes/招牌黄金花椒鸡汤.jpg', '/uploads/dishes/招牌雪花牛肉.jpg', 350, 0, -4400, '/uploads/restaurants/辉哥火锅.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (24, '第六季自助餐厅', '自助餐', 39464, 4.4, 104551, '单人午餐卷（289）', '单人晚餐卷（309）', '快乐星期一单人午餐（309）', '/uploads/dishes/单人午餐卷.jpg', '/uploads/dishes/单人晚餐卷.jpg', '/uploads/dishes/快乐星期一单人午餐.jpg', 349, 1100, 1600, '/uploads/restaurants/第六季自助餐厅.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (25, '疆来', '新疆菜', 2515, 4.4, 4567, '面包羊腿（189）', '新疆大盘鸡（98）', '八毛凉皮（32）', '/uploads/dishes/面包羊腿.jpg', '/uploads/dishes/新疆大盘鸡.jpg', '/uploads/dishes/八毛凉皮.jpg', 88, -400, 1000, '/uploads/restaurants/疆来.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (26, '杨记老门框百年卤煮', '北京菜', 326, 3.9, 623, '老北京爆肚（48）', '老北京炸酱面（25）', '精品卤煮（45）', '/uploads/dishes/老北京爆肚.jpg', '/uploads/dishes/老北京炸酱面.jpg', '/uploads/dishes/精品卤煮.jpg', 40, 410, 0, '/uploads/restaurants/杨记老门框百年卤煮.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (27, '利桥顺酒楼', '北京菜', 6432, 4.3, 45431, '招牌驴紫盖（258）', '抓炒鲈鱼（158）', '全丝驴胶（58）', '/uploads/dishes/招牌驴紫盖.jpg', '/uploads/dishes/抓炒鲈鱼.jpg', '/uploads/dishes/全丝驴胶.jpg', 85, -860, 440, '/uploads/restaurants/利桥顺酒楼.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (28, '朴秀英韩国传统烤肉', '韩国菜', 17999, 4.4, 95529, '厚切排酸黑猪五花肉（68）', '雪花牛肋条（98）', '冠军冷面（28）', '/uploads/dishes/厚切排酸黑猪五花肉.jpg', '/uploads/dishes/雪花牛肋条.jpg', '/uploads/dishes/冠军冷面.jpg', 130, 1000, 2000, '/uploads/restaurants/朴秀英韩国传统烤肉.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (29, '山河屯铁锅炖', '东北菜', 6896, 4.4, 26269, '铁锅炖大鹅（298）', '排骨锅（258）', '花卷（19）', '/uploads/dishes/铁锅炖大鹅.jpg', '/uploads/dishes/排骨锅.jpg', '/uploads/dishes/花卷.jpg', 93, 450, 950, '/uploads/restaurants/山河屯铁锅炖.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (30, '嘿！延边炭火烤肉', '东北菜', 3376, 4.8, 19150, '延杰冷面（21.8）', '咱家肥牛肉（39.8）', '咱家瘦牛肉（42.8）', '/uploads/dishes/延杰冷面.jpg', '/uploads/dishes/咱家肥牛肉.jpg', '/uploads/dishes/咱家瘦牛肉.jpg', 90, 340, 0, '/uploads/restaurants/嘿！延边炭火烤肉.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (31, '祁小小的小面', '西北菜', 651, 4.1, 469, '招牌牛肉面（29.9）', '豌杂面（22）', '招牌重庆小面（18）', '/uploads/dishes/招牌牛肉面.jpg', '/uploads/dishes/豌杂面.jpg', '/uploads/dishes/招牌重庆小面.jpg', 23, 450, 950, '/uploads/restaurants/祁小小的小面.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (32, '晋来顺手擀面', '山西菜', 186, 3.7, 297, '特色三合一拌面（26）', '扁豆肉丝拌面（26）', '茄子肉丝拌面（22）', '/uploads/dishes/特色三合一拌面.jpg', '/uploads/dishes/扁豆肉丝拌面.jpg', '/uploads/dishes/茄子肉丝拌面.jpg', 27, 100, -400, '/uploads/restaurants/晋来顺手擀面.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (33, '龚二姐湖南粉面馆', '湖南菜', 4285, 4.1, 48779, '头牌牛肉雪里红（35）', '长沙原汤肉丝（24）', '特色麻辣牛肉面（29）', '/uploads/dishes/头牌牛肉雪里红.jpg', '/uploads/dishes/长沙原汤肉丝.jpg', '/uploads/dishes/特色麻辣牛肉面.jpg', 39, 700, 2400, '/uploads/restaurants/龚二姐湖南粉面馆.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (34, '黔粉堂', '贵州菜', 1651, 4.2, 3645, '贵州牛肉粉（34）', '贵州羊肉粉（29）', '招牌贵州带皮羊肉粉（34）', '/uploads/dishes/贵州牛肉粉.jpg', '/uploads/dishes/贵州羊肉粉.jpg', '/uploads/dishes/招牌贵州带皮羊肉粉.jpg', 31, 450, 950, '/uploads/restaurants/黔粉堂.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (35, '姥姥家春饼店', '北京菜', 7825, 4.2, 32551, '秘制肘子（46）', '炒合菜（32）', '香辣肉丝（39）', '/uploads/dishes/秘制肘子.jpg', '/uploads/dishes/炒合菜.jpg', '/uploads/dishes/香辣肉丝.jpg', 63, -250, -950, '/uploads/restaurants/姥姥家春饼店.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (36, '金谷园饺子馆', '北京菜', 14702, 4.1, 133917, '葱麻鸡（15）', '鲅鱼饺子（15）', '青瓜鲜虾饺子（18）', '/uploads/dishes/葱麻鸡.jpg', '/uploads/dishes/鲅鱼饺子.jpg', '/uploads/dishes/青瓜鲜虾饺子.jpg', 52, 500, 950, '/uploads/restaurants/金谷园饺子馆.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (37, '潇湘阁', '湖南菜', 2807, 4.5, 12946, '金钱蛋（39）', '小炒黄牛肉（49）', '肉末茄子煲（35）', '/uploads/dishes/金钱蛋.jpg', '/uploads/dishes/小炒黄牛肉.jpg', '/uploads/dishes/肉末茄子煲.jpg', 79, -3000, 2000, '/uploads/restaurants/潇湘阁.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (38, '竹叶刷肉店', '北京菜', 2129, 4.7, 11279, '炭烧羊肉串（6）', '手切鲜羊后腿（57）', '手切极品鲜羊肉（59）', '/uploads/dishes/炭烧羊肉串.jpg', '/uploads/dishes/手切鲜羊后腿.jpg', '/uploads/dishes/手切极品鲜羊肉.jpg', 108, 900, 2100, '/uploads/restaurants/竹叶刷肉店.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (39, '兰蕙餐厅', '北京菜', 175, 3.6, 143, '铁板牛柳（58）', '椒盐虾（118）', '薯泥沙拉（28）', '/uploads/dishes/铁板牛柳.jpg', '/uploads/dishes/椒盐虾.jpg', '/uploads/dishes/薯泥沙拉.jpg', 53, 500, 50, '/uploads/restaurants/兰蕙餐厅.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (40, '拾来运转铁锅炖', '东北菜', 4565, 4.6, 3258, '小鸡排骨锅（49）', '拾来运转铁锅炖大鹅（238）', '宽粉（12）', '/uploads/dishes/小鸡排骨锅.jpg', '/uploads/dishes/拾来运转铁锅炖大鹅.jpg', '/uploads/dishes/宽粉.jpg', 75, -600, 1000, '/uploads/restaurants/拾来运转铁锅炖.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (41, '胶东海鲜', '北京菜', 1290, 4.1, 26807, '海肠捞饭（118）', '炸舌头鱼（38）', '皮皮虾（118）', '/uploads/dishes/海肠捞饭.jpg', '/uploads/dishes/炸舌头鱼.jpg', '/uploads/dishes/皮皮虾.jpg', 115, 640, 0, '/uploads/restaurants/胶东海鲜.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (42, '九本居酒屋', '日本菜', 8978, 4.8, 52151, '油梨鳗鱼卷（78）', '厚切三文鱼（59.9）', '雪花牛肉寿喜烧（298）', '/uploads/dishes/油梨鳗鱼卷.jpg', '/uploads/dishes/厚切三文鱼.jpg', '/uploads/dishes/雪花牛肉寿喜烧.jpg', 152, -600, -500, '/uploads/restaurants/九本居酒屋.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (43, '沔湘纪餐厅', '湖北菜', 950, 4.8, 2807, '沔阳三蒸（198）', '擂椒茄子皮蛋（39）', '双椒水库鱼头（298）', '/uploads/dishes/沔阳三蒸.jpg', '/uploads/dishes/擂椒茄子皮蛋.jpg', '/uploads/dishes/双椒水库鱼头.jpg', 112, -550, 0, '/uploads/restaurants/沔湘纪餐厅.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (44, '天外天烤鸭店', '北京菜', 736, 3.9, 1283, '京酱肉丝（46）', '精品烤鸭（188）', '贝勒烤肉（68）', '/uploads/dishes/京酱肉丝.jpg', '/uploads/dishes/精品烤鸭.jpg', '/uploads/dishes/贝勒烤肉.jpg', 85, -200, 1400, '/uploads/restaurants/天外天烤鸭店.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (45, '博士居香辣滋味重庆小面', '重庆菜', 403, 4, 1963, '口水鸡（16）', '豌杂面 （18）', '老麻抄手（21）', '/uploads/dishes/口水鸡.jpg', '/uploads/dishes/豌杂面.jpg', '/uploads/dishes/老麻抄手.jpg', 27, 150, 850, '/uploads/restaurants/博士居香辣滋味重庆小面.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (46, '方中山胡辣汤', '北京菜', 807, 4.1, 3287, '牛肉盒子（10）', '传统胡辣汤（12）', '葱油饼（8）', '/uploads/dishes/牛肉盒子.jpg', '/uploads/dishes/传统胡辣汤.jpg', '/uploads/dishes/葱油饼.jpg', 21, 550, 950, '/uploads/restaurants/方中山胡辣汤.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (47, '八旗烤肉', '北京菜', 10290, 4.8, 87167, '炙烤牛肉（56）', '烤五花肉（48）', '烤口蘑（18）', '/uploads/dishes/炙烤牛肉.jpg', '/uploads/dishes/烤五花肉.jpg', '/uploads/dishes/烤口蘑.jpg', 107, 1800, 600, '/uploads/restaurants/八旗烤肉.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (48, '一喜日本料理', '日本菜', 3141, 4.2, 7713, '鳗鱼虾卷（85）', '三文鱼刺身（65）', '绯闻（82）', '/uploads/dishes/鳗鱼虾卷.jpg', '/uploads/dishes/三文鱼刺身.jpg', '/uploads/dishes/绯闻.jpg', 110, 1300, 600, '/uploads/restaurants/一喜日本料理.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (49, '犟骨头', '北京菜', 650, 3.9, 274, '犟骨头（37.5）', '犟心牛腩饭（42）', '酱脊骨（24）', '/uploads/dishes/犟骨头.jpg', '/uploads/dishes/犟心牛腩饭.jpg', '/uploads/dishes/酱脊骨.jpg', 35, 300, 0, '/uploads/restaurants/犟骨头.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (50, '北京东方红酒楼', '湘菜', 2480, 4.2, 6129, '剁椒鱼头（139）', '小炒黄牛肉（99）', '老面馒头（9）', '/uploads/dishes/剁椒鱼头.jpg', '/uploads/dishes/小炒黄牛肉.jpg', '/uploads/dishes/老面馒头.jpg', 200, -300, 2100, '/uploads/restaurants/北京东方红酒楼.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (51, '白鹿江西餐厅', '江西菜', 1755, 4.2, 6209, '井冈山烟笋炒肉（46）', '瓦罐汤（22）', '糯香排骨（48）', '/uploads/dishes/井冈山烟笋炒肉.jpg', '/uploads/dishes/瓦罐汤.jpg', '/uploads/dishes/糯香排骨.jpg', 74, 900, 800, '/uploads/restaurants/白鹿江西餐厅.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (52, '鱼籽村秘制拌饭', '韩国菜', 956, 4.4, 746, '原味鱼籽拌饭（19）', '蒜香芝士鱼籽拌饭（36）', '微辣鱼籽拌饭（22）', '/uploads/dishes/原味鱼籽拌饭.jpg', '/uploads/dishes/蒜香芝士鱼籽拌饭.jpg', '/uploads/dishes/微辣鱼籽拌饭.jpg', 28, 400, 900, '/uploads/restaurants/鱼籽村秘制拌饭.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (53, '禾谷园', '东北菜', 198, 3.8, 316, '宫保鸡丁（32）', '清炒大虾仁（58）', '猪肉大葱饺子（8）', '/uploads/dishes/宫保鸡丁.jpg', '/uploads/dishes/清炒大虾仁.jpg', '/uploads/dishes/猪肉大葱饺子.jpg', 34, -400, 1000, '/uploads/restaurants/禾谷园.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (54, '梨花盏核桃碳烤肉', '韩国菜', 4880, 4.6, 5607, '韩式烤肠（45）', '梨花盏家庭拌肉（52）', '鲜嫩大片鸡腿（38）', '/uploads/dishes/韩式烤肠.jpg', '/uploads/dishes/梨花盏家庭拌肉.jpg', '/uploads/dishes/鲜嫩大片鸡腿.jpg', 74, 500, 900, '/uploads/restaurants/梨花盏核桃碳烤肉.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (55, '梧桐欣雨金渔川菜万州烤鱼家常菜', '川菜', 2055, 4.4, 27266, '麻辣味烤鱼（168）', '香辣味烤鱼（168）', '辣子鸡（62.8）', '/uploads/dishes/麻辣味烤鱼.jpg', '/uploads/dishes/香辣味烤鱼.jpg', '/uploads/dishes/辣子鸡.jpg', 118, -200, 1000, '/uploads/restaurants/梧桐欣雨金渔川菜万州烤鱼家常菜.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (56, '辣香居梁记干锅鸭头', '北京菜', 794, 4.7, 3315, '梁记全家福（198）', '精品老鸭汤（98）', '干锅鸭头（98）', '/uploads/dishes/梁记全家福.jpg', '/uploads/dishes/精品老鸭汤.jpg', '/uploads/dishes/干锅鸭头.jpg', 66, 500, 900, '/uploads/restaurants/辣香居梁记干锅鸭头.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (57, '伊豆野菜村', '日本菜', 7257, 4.5, 24999, '寿喜锅（128）', '上脑（268）', '牛肩（128）', '/uploads/dishes/寿喜锅.jpg', '/uploads/dishes/上脑.jpg', '/uploads/dishes/牛肩.jpg', 158, 200, -500, '/uploads/restaurants/伊豆野菜村.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (58, '十里香川菜', '川菜', 459, 4.3, 144, '精品酸菜鱼（52）', '拍黄瓜（16）', '京酱肉丝（42）', '/uploads/dishes/精品酸菜鱼.jpg', '/uploads/dishes/拍黄瓜.jpg', '/uploads/dishes/京酱肉丝.jpg', 55, -300, 1200, '/uploads/restaurants/十里香川菜.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (59, '海底捞火锅', '川菜', 43308, 4.5, 15509, '招牌虾滑（68）', '血旺（40）', '内蒙草原羔羊肉（62）', '/uploads/dishes/招牌虾滑.jpg', '/uploads/dishes/血旺.jpg', '/uploads/dishes/内蒙草原羔羊肉.jpg', 115, 0, -500, '/uploads/restaurants/海底捞火锅.jpg');
INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `dishes_image`, `dishes_image1`, `dishes_image2`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES (60, '珍滋味海鲜火锅酒家', '北京菜', 1008, 4.8, 6758, '招牌佛跳墙火锅（358）', '招牌瑶柱粥（78）', '龙虾海鲜拼盘（498）', '/uploads/dishes/招牌佛跳墙火锅.jpg', '/uploads/dishes/招牌瑶柱粥.jpg', '/uploads/dishes/龙虾海鲜拼盘.jpg', 333, -1000, 1000, '/uploads/restaurants/珍滋味海鲜火锅酒家.jpg');
COMMIT;

-- ----------------------------
-- Table structure for restaurant_favorite
-- ----------------------------
DROP TABLE IF EXISTS `restaurant_favorite`;
CREATE TABLE `restaurant_favorite` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `restaurant_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_restaurant_user_favorite` (`restaurant_id`,`user_id`),
  KEY `idx_restaurant_favorite_user_id` (`user_id`),
  KEY `idx_restaurant_favorite_restaurant_id` (`restaurant_id`),
  CONSTRAINT `restaurant_favorite_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  CONSTRAINT `restaurant_favorite_ibfk_2` FOREIGN KEY (`restaurant_id`) REFERENCES `restaurant` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of restaurant_favorite
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for restaurant_comment
-- ----------------------------
DROP TABLE IF EXISTS `restaurant_comment`;
CREATE TABLE `restaurant_comment` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `restaurant_id` int NOT NULL,
  `content` text NOT NULL,
  `rating` float NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_restaurant_comment_user_id` (`user_id`),
  KEY `idx_restaurant_comment_restaurant_id` (`restaurant_id`),
  CONSTRAINT `restaurant_comment_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  CONSTRAINT `restaurant_comment_ibfk_2` FOREIGN KEY (`restaurant_id`) REFERENCES `restaurant` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of restaurant_comment
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for user_cart
-- ----------------------------
DROP TABLE IF EXISTS `user_cart`;
CREATE TABLE `user_cart` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `dish_name` varchar(255) NOT NULL,
  `dish_price` decimal(10,2) NOT NULL,
  `dish_image` varchar(255) DEFAULT NULL,
  `restaurant_id` int NOT NULL,
  `restaurant_name` varchar(255) NOT NULL,
  `quantity` int NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_cart_user_id` (`user_id`),
  KEY `idx_user_cart_restaurant_id` (`restaurant_id`),
  CONSTRAINT `user_cart_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  CONSTRAINT `user_cart_ibfk_2` FOREIGN KEY (`restaurant_id`) REFERENCES `restaurant` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of user_cart
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for restaurant_rating
-- ----------------------------
DROP TABLE IF EXISTS `restaurant_rating`;
CREATE TABLE `restaurant_rating` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `restaurant_id` INT NOT NULL,
  `user_id` INT NOT NULL,
  `rating` DECIMAL(2,1) NOT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_restaurant` (`user_id`, `restaurant_id`),
  INDEX `idx_restaurant_id` (`restaurant_id`),
  INDEX `idx_user_id` (`user_id`),
  CONSTRAINT `restaurant_rating_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  CONSTRAINT `restaurant_rating_ibfk_2` FOREIGN KEY (`restaurant_id`) REFERENCES `restaurant` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of restaurant_rating
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for vertexes2 (朝阳公园地点信息)
-- ----------------------------
DROP TABLE IF EXISTS `vertexes2`;
CREATE TABLE `vertexes2` (
  `vertex_id` int NOT NULL,
  `x` int NOT NULL,
  `y` int NOT NULL,
  `label` varchar(255) DEFAULT NULL,
  `type` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`vertex_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of vertexes2
-- ----------------------------
BEGIN;
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (1, 233, 1613, '【1线】蓝色港湾站', '电瓶车站');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (2, 250, 1489, '亮码头停车场', '停车场');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (3, 263, 1438, '08广场北路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (4, 274, 1394, '【1线】08广场站', '电瓶车站');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (5, 203, 1418, '蔚来换电站', '服务设施');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (6, 287, 1430, '08广场南路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (7, 196, 1377, '换电站东南路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (8, 108, 1355, '伯宁花园', '其他设施');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (9, 126, 1275, '地铁枣营站A口', '交通设施');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (10, 297, 1298, '【1线】08广场南站', '电瓶车站');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (11, 347, 1298, '太空归来西北路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (12, 434, 1302, '太空归来', '景观点');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (13, 599, 1342, '荷花湖', '景观点');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (14, 349, 1185, '【1线】雾泉站', '电瓶车站');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (15, 468, 1198, '莫扎特雕像', '景观点');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (16, 578, 1274, '滨水之舟游船码头', '码头');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (17, 571, 1195, '滨水之舟', '景观点');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (18, 147, 1043, '【1线】地铁枣营站D口站', '电瓶车站');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (19, 239, 1042, '泡泡玛特城市乐园', '景观点');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (20, 340, 1034, '【1线】泡泡玛特城市乐园站', '电瓶车站');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (21, 346, 1157, '雾泉南路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (22, 253, 1290, '枣营站东北路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (23, 252, 1168, '地铁枣营站停车场东路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (24, 208, 1165, '地铁枣营站停车场', '停车场');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (25, 408, 1092, '融合广场西路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (26, 495, 1097, '融合广场', '景观点');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (27, 558, 1095, '萌溪亲子营地乐园', '娱乐设施');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (28, 424, 1009, '泡泡玛特城市乐园游客中心', '服务设施');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (29, 351, 969, '马术中心北路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (30, 151, 995, '【换乘站】枣子营街站', '电瓶车站');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (31, 318, 959, '马术中心', '场馆');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (32, 342, 862, '马术中心南路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (33, 164, 735, '北小湖', '景观点');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (34, 172, 586, '【2线】卡萨酒吧站', '电瓶车站');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (35, 235, 582, '北小湖停车场', '停车场');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (36, 257, 555, '安提瓜和巴布达驻华大使馆-商务处', '其他设施');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (37, 352, 541, 'U2GO沉浸式数字体验中心', '场馆');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (38, 283, 459, '粤凤佬·臻品餐厅', '餐厅');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (39, 429, 486, '8号温泉商务酒店停车场', '停车场');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (40, 459, 394, '8号温泉商务酒店东门', '酒店');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (41, 159, 331, '【2线】金台艺术馆站', '电瓶车站');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (42, 200, 301, '金台艺术馆北路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (43, 232, 258, '金台艺术馆', '场馆');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (44, 169, 213, '西蒙玻利瓦尔雕像', '景观点');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (45, 15, 147, '凤凰中心', '场馆');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (46, 215, 141, '阿拜库南巴耶夫雕像', '景观点');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (47, 0, 0, '【2线】凤凰中心站', '电瓶车站');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (48, 222, 62, '凤凰中心东路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (49, 265, 1, '凤凰中心东卫生间', '卫生间');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (50, 388, 52, '地铁朝阳公园站A口北路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (51, 388, 2, '地铁朝阳公园站A口', '地铁站');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (52, 222, 455, '大使馆南停车场', '停车场');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (53, 226, 378, '商务酒店西南路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (54, 286, 372, '商务酒店南1号路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (55, 595, 2, '【2线】地铁朝阳公园站E口', '电瓶车站');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (56, 500, 69, '地铁朝阳公园站北路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (57, 503, 225, '地铁朝阳公园站停车场', '停车场');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (58, 569, 54, '地铁朝阳公园站B口', '地铁站');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (59, -47, 1742, '巡逻警卫站', '服务设施');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (60, 204, 1323, '中石化加油站', '服务设施');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (61, 138, 1136, '地铁枣营站', '地铁站');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (62, -5, 82, '朝阳公园路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (63, 423, 357, '8号温泉商务酒店南门', '酒店');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (64, 286, 358, '商务酒店南2号路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (65, 226, 354, '金台艺术馆停车场', '停车场');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (66, 591, 681, '激流勇进', '娱乐设施');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (67, 621, 715, '激流勇进东北路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (68, 736, 780, '商亭', '商店');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (69, 734, 717, '商亭南路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (70, 836, 717, '樱花谷西路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (71, 837, 656, '太空漫步', '娱乐设施');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (72, 862, 610, '恐龙王国西路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (73, 735, 679, '勇敢者乐园', '娱乐设施');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (74, 803, 643, '太空漫步西路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (75, 634, 773, '方舟湖码头', '码头');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (76, 604, 573, '激流勇进南路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (77, 625, 540, '【2线】2号码头站', '电瓶车站');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (78, 596, 503, '2号码头卫生间西门', '卫生间');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (79, 573, 485, '2号码头北路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (80, 604, 463, '2号码头', '码头');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (81, 562, 437, '索道上站', '娱乐设施');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (82, 661, 496, '2号码头东路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (83, 698, 528, '游乐场西南路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (84, 715, 471, '【2线】游乐场站', '电瓶车站');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (85, 817, 577, '游乐场东1号路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (86, 863, 578, '游乐场东2号路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (87, 882, 529, '世纪喷泉北路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (88, 906, 581, '恐龙王国南路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (89, 983, 579, '恐龙王国卫生间', '卫生间');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (90, 969, 658, '童话火车', '娱乐设施');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (91, 892, 717, '樱花谷', '景观点');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (92, 887, 678, '樱花谷南路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (93, 890, 641, '恐龙王国历险城', '娱乐设施');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (94, 941, 513, '世纪喷泉东北路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (95, 835, 511, '世纪喷泉西北路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (96, 791, 484, '游乐场东南2号路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (97, 771, 520, '游乐场东南1号路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (98, 805, 461, '世纪喷泉西路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (99, 861, 460, '世纪喷泉', '景观点');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (100, 862, 403, '世纪喷泉广场', '景观点');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (101, 635, 478, '2号码头卫生间东门', '卫生间');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (102, 658, 436, '2号码头东南路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (103, 729, 428, '索尼探梦西北路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (104, 814, 411, '网易科创思维馆', '场馆');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (105, 731, 375, '索尼探梦', '场馆');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (106, 702, 378, '索尼探梦西路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (107, 685, 371, '落日飞车北路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (108, 715, 298, '落日飞车', '娱乐设施');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (109, 746, 311, '【2线】落日飞车站', '电瓶车站');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (110, 815, 314, '礼花广场北路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (111, 895, 312, '世纪喷泉南路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (112, 932, 379, '世纪喷泉东路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (113, 831, 221, '礼花广场', '景观点');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (114, 912, 199, '礼花广场东路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (115, 756, 208, '礼花广场西路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (116, 768, 130, '【2线】游船码头站', '电瓶车站');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (117, 748, 100, '游船码头', '码头');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (118, 706, 78, '三夫户外', '商店');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (119, 822, 96, '【2线】下沉广场站', '电瓶车站');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (120, 909, 116, '下沉广场东路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (121, 911, 64, '北京万科中心', '场馆');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (122, 911, 0, '南1门', '大门');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (123, 821, 0, '【2线】南门站', '电瓶车站');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (124, 793, 74, '南门西北路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (125, 180, 434, '朝阳公园西路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (126, 642, 64, '索道下站', '娱乐设施');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (127, 735, 577, '游乐场', '娱乐设施');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (128, 345, 1124, '泡泡玛特北路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (129, 675, 575, '游乐场西1号路口', '路口');
INSERT INTO `vertexes2` (`vertex_id`, `x`, `y`, `label`, `type`) VALUES (130, 678, 558, '游乐场西2号路口', '路口');
COMMIT;

-- ----------------------------
-- Table structure for edges2 (朝阳公园路径信息)
-- ----------------------------
DROP TABLE IF EXISTS `edges2`;
CREATE TABLE `edges2` (
  `id` int NOT NULL AUTO_INCREMENT,
  `src_id` int NOT NULL,
  `des_id` int NOT NULL,
  `weight` int NOT NULL,
  `crowding` double DEFAULT NULL,
  `is_car` int DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of edges2 (第一部分：基础路径数据)
-- ----------------------------
BEGIN;
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (1, 1, 2, 124, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (2, 1, 59, 178, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (3, 2, 3, 42, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (4, 3, 4, 55, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (5, 3, 5, 62, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (6, 5, 7, 40, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (7, 4, 6, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (8, 6, 10, 40, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (9, 6, 60, 80, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (10, 7, 8, 85, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (11, 7, 60, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (12, 8, 9, 84, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (13, 8, 59, 180, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (14, 9, 60, 80, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (15, 9, 22, 120, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (16, 9, 61, 115, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (17, 10, 22, 45, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (18, 10, 11, 45, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (19, 11, 12, 90, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (20, 11, 14, 95, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (21, 12, 13, 175, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (22, 13, 16, 90, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (23, 14, 15, 120, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (24, 14, 21, 38, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (25, 15, 17, 100, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (26, 15, 26, 105, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (27, 16, 17, 92, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (28, 17, 27, 80, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (29, 18, 61, 120, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (30, 18, 19, 90, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (31, 18, 30, 50, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (32, 19, 20, 100, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (33, 19, 24, 140, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (34, 20, 28, 93, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (35, 20, 29, 66, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (36, 20, 128, 90, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (37, 21, 128, 30, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (38, 21, 23, 95, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (39, 22, 23, 123, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (40, 23, 24, 45, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (41, 25, 128, 77, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (42, 25, 26, 80, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (43, 25, 28, 100, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (44, 26, 27, 70, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (45, 29, 31, 31, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (46, 30, 33, 263, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (47, 30, 32, 228, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (48, 31, 32, 100, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (49, 32, 66, 300, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (50, 33, 34, 143, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (51, 33, 35, 70, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (52, 34, 35, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (53, 34, 41, 255, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (54, 35, 36, 40, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (55, 35, 52, 130, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (56, 36, 37, 95, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (57, 36, 52, 125, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (58, 37, 39, 100, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (59, 38, 52, 25, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (60, 38, 53, 80, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (61, 39, 40, 95, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (62, 40, 63, 40, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (63, 41, 42, 45, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (64, 42, 43, 50, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (65, 42, 44, 90, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (66, 43, 65, 30, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (67, 44, 46, 50, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (68, 45, 47, 15, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (69, 45, 62, 150, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (70, 46, 48, 80, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (71, 47, 48, 222, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (72, 48, 49, 45, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (73, 48, 50, 166, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (74, 49, 125, 85, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (75, 50, 51, 50, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (76, 50, 56, 112, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (77, 51, 55, 207, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (78, 52, 53, 80, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (79, 53, 54, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (80, 53, 65, 25, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (81, 54, 63, 140, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (82, 54, 64, 15, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (83, 55, 58, 26, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (84, 55, 123, 226, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (85, 56, 57, 156, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (86, 56, 58, 69, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (87, 57, 126, 139, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (88, 58, 126, 73, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (89, 62, 125, 175, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (90, 63, 64, 137, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (91, 64, 65, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (92, 66, 67, 50, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (93, 66, 75, 45, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (94, 66, 76, 110, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (95, 67, 68, 115, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (96, 68, 69, 65, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (97, 69, 70, 102, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (98, 69, 73, 40, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (99, 70, 71, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (100, 70, 91, 55, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (101, 71, 72, 50, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (102, 71, 74, 35, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (103, 72, 93, 30, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (104, 73, 74, 70, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (105, 73, 129, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (106, 74, 85, 15, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (107, 75, 129, 45, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (108, 76, 77, 30, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (109, 76, 129, 70, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (110, 77, 78, 30, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (111, 77, 79, 55, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (112, 78, 101, 40, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (113, 79, 80, 30, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (114, 79, 81, 50, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (115, 80, 81, 30, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (116, 80, 82, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (117, 81, 102, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (118, 82, 83, 40, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (119, 83, 84, 45, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (120, 83, 130, 20, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (121, 84, 96, 80, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (122, 84, 97, 50, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (123, 85, 86, 45, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (124, 85, 97, 50, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (125, 86, 87, 50, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (126, 86, 88, 25, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (127, 87, 95, 50, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (128, 87, 94, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (129, 88, 89, 80, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (130, 88, 93, 40, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (131, 89, 90, 80, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (132, 90, 91, 80, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (133, 91, 92, 40, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (134, 92, 93, 40, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (135, 94, 112, 70, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (136, 95, 98, 30, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (137, 96, 98, 15, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (138, 97, 98, 35, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (139, 98, 99, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (140, 99, 100, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (141, 99, 111, 80, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (142, 100, 104, 50, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (143, 100, 110, 50, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (144, 101, 102, 25, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (145, 102, 103, 70, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (146, 103, 105, 55, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (147, 104, 110, 20, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (148, 105, 106, 30, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (149, 106, 107, 20, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (150, 107, 108, 75, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (151, 108, 109, 30, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (152, 109, 115, 50, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (153, 110, 111, 80, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (154, 111, 112, 40, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (155, 111, 113, 80, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (156, 112, 121, 70, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (157, 113, 114, 80, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (158, 113, 115, 75, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (159, 114, 120, 80, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (160, 115, 116, 80, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (161, 116, 117, 30, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (162, 116, 119, 55, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (163, 117, 118, 45, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (164, 117, 124, 50, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (165, 118, 126, 65, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (166, 119, 120, 90, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (167, 119, 123, 100, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (168, 120, 121, 55, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (169, 121, 122, 65, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (170, 122, 123, 90, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (171, 123, 124, 30, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (172, 124, 126, 70, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (173, 125, 38, 100, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (174, 127, 129, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (175, 127, 130, 120, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (176, 129, 130, 20, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (177, 2, 1, 124, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (178, 59, 1, 178, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (179, 3, 2, 42, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (180, 4, 3, 55, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (181, 5, 3, 62, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (182, 7, 5, 40, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (183, 6, 4, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (184, 10, 6, 40, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (185, 60, 6, 80, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (186, 8, 7, 85, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (187, 60, 7, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (188, 9, 8, 84, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (189, 59, 8, 180, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (190, 60, 9, 80, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (191, 22, 9, 120, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (192, 61, 9, 115, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (193, 22, 10, 45, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (194, 11, 10, 45, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (195, 12, 11, 90, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (196, 14, 11, 95, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (197, 13, 12, 175, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (198, 16, 13, 90, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (199, 15, 14, 120, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (200, 21, 14, 38, 1, 0);
COMMIT;
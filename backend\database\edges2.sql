INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (1, 1, 2, 124, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (2, 1, 59, 178, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (3, 2, 3, 42, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (4, 3, 4, 55, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (5, 3, 5, 62, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (6, 5, 7, 40, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (7, 4, 6, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (8, 6, 10, 40, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (9, 6, 60, 80, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (10, 7, 8, 85, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (11, 7, 60, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (12, 8, 9, 84, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (13, 8, 59, 180, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (14, 9, 60, 80, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (15, 9, 22, 120, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (16, 9, 61, 115, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (17, 10, 22, 45, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (18, 10, 11, 45, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (19, 11, 12, 90, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (20, 11, 14, 95, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (21, 12, 13, 175, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (22, 13, 16, 90, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (23, 14, 15, 120, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (24, 14, 21, 38, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (25, 15, 17, 100, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (26, 15, 26, 105, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (27, 16, 17, 92, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (28, 17, 27, 80, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (29, 18, 61, 120, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (30, 18, 19, 90, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (31, 18, 30, 50, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (32, 19, 20, 100, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (33, 19, 24, 140, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (34, 20, 28, 93, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (35, 20, 29, 66, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (36, 20, 128, 90, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (37, 21, 128, 30, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (38, 21, 23, 95, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (39, 22, 23, 123, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (40, 23, 24, 45, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (41, 25, 128, 77, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (42, 25, 26, 80, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (43, 25, 28, 100, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (44, 26, 27, 70, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (45, 29, 31, 31, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (46, 30, 33, 263, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (47, 30, 32, 228, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (48, 31, 32, 100, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (49, 32, 66, 300, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (50, 33, 34, 143, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (51, 34, 35, 70, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (52, 34, 125, 137, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (53, 35, 36, 35, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (54, 36, 37, 90, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (55, 36, 38, 100, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (56, 38, 52, 63, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (57, 38, 39, 145, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (58, 39, 40, 100, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (59, 40, 63, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (60, 41, 42, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (61, 41, 125, 110, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (62, 41, 45, 270, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (63, 42, 43, 50, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (64, 42, 44, 95, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (65, 42, 65, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (66, 44, 46, 55, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (67, 45, 62, 81, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (68, 46, 48, 100, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (69, 47, 62, 80, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (70, 47, 49, 260, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (71, 48, 50, 153, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (72, 49, 51, 142, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (73, 50, 51, 40, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (74, 50, 56, 115, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (75, 51, 55, 203, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (76, 52, 125, 50, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (77, 52, 53, 76, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (78, 53, 54, 57, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (79, 54, 63, 130, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (80, 54, 64, 13, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (81, 55, 123, 226, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (82, 55, 58, 50, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (83, 56, 57, 160, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (84, 56, 58, 70, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (85, 58, 126, 70, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (86, 64, 65, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (87, 66, 67, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (88, 66, 76, 120, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (89, 67, 69, 110, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (90, 68, 75, 100, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (91, 68, 69, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (92, 69, 70, 100, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (93, 69, 73, 37, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (94, 70, 71, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (95, 70, 91, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (96, 71, 74, 40, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (97, 71, 92, 45, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (98, 71, 72, 45, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (99, 72, 86, 36, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (100, 72, 93, 40, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (101, 73, 74, 80, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (102, 73, 127, 93, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (103, 74, 85, 70, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (104, 76, 77, 40, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (105, 76, 129, 73, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (106, 77, 78, 42, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (107, 77, 82, 56, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (108, 77, 130, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (109, 78, 79, 25, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (110, 79, 80, 40, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (111, 80, 81, 40, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (112, 80, 101, 40, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (113, 82, 101, 40, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (114, 82, 84, 55, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (115, 83, 130, 40, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (116, 83, 127, 55, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (117, 83, 97, 70, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (118, 83, 84, 50, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (119, 84, 96, 75, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (120, 84, 103, 40, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (121, 85, 86, 40, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (122, 85, 121, 30, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (123, 85, 127, 80, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (124, 86, 87, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (125, 86, 88, 30, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (126, 87, 88, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (127, 87, 94, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (128, 87, 95, 50, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (129, 88, 89, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (130, 88, 93, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (131, 89, 90, 80, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (132, 90, 92, 90, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (133, 91, 92, 40, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (134, 92, 93, 40, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (135, 94, 112, 140, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (136, 95, 96, 55, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (137, 95, 121, 35, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (138, 96, 97, 40, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (139, 96, 98, 32, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (140, 97, 127, 65, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (141, 98, 99, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (142, 98, 104, 50, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (143, 99, 100, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (144, 101, 102, 54, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (145, 102, 103, 65, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (146, 102, 106, 65, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (147, 103, 104, 85, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (148, 103, 105, 53, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (149, 104, 110, 93, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (150, 105, 109, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (151, 105, 106, 30, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (152, 106, 107, 16, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (153, 107, 108, 70, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (154, 108, 109, 35, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (155, 109, 110, 70, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (156, 109, 115, 103, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (157, 110, 111, 70, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (158, 110, 113, 91, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (159, 111, 114, 80, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (160, 111, 112, 114, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (161, 113, 114, 75, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (162, 113, 115, 83, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (163, 114, 120, 80, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (164, 115, 116, 86, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (165, 116, 117, 30, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (166, 116, 119, 50, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (167, 117, 124, 53, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (168, 117, 118, 30, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (169, 118, 126, 43, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (170, 119, 120, 80, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (171, 119, 124, 40, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (172, 119, 123, 90, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (173, 120, 121, 50, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (174, 121, 122, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (175, 122, 123, 90, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (176, 127, 129, 60, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (177, 129, 130, 17, 1, 0);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (178, 1, 4, 220, 1, 1);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (179, 4, 10, 100, 1, 1);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (180, 10, 11, 50, 1, 1);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (181, 11, 14, 95, 1, 1);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (182, 14, 20, 77, 1, 1);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (183, 20, 18, 201, 1, 1);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (184, 18, 30, 50, 1, 1);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (185, 66, 32, 189, 1, 2);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (186, 32, 30, 230, 1, 2);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (187, 30, 34, 420, 1, 2);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (188, 34, 125, 140, 1, 2);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (189, 125, 41, 120, 1, 2);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (190, 41, 45, 239, 1, 2);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (191, 45, 62, 80, 1, 2);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (192, 62, 47, 80, 1, 2);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (193, 47, 55, 590, 1, 2);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (194, 55, 123, 227, 1, 2);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (195, 123, 119, 94, 1, 2);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (196, 119, 116, 60, 1, 2);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (197, 116, 109, 190, 1, 2);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (198, 109, 84, 160, 1, 2);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (199, 84, 77, 112, 1, 2);
INSERT INTO `edges2` (`id`, `src_id`, `des_id`, `weight`, `crowding`, `is_car`) VALUES (200, 77, 66, 150, 1, 2);

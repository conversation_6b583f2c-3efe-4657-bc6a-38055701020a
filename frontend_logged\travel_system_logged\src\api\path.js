import axios from 'axios'

/**
 * 多条件查询景点
 * @param {Object} params - 查询参数
 * @param {number} [params.startVertexId] - 起始顶点ID
 * @param {string} [params.locationName] - 起始地点名称
 * @param {number} params.distance - 搜索距离（米）
 * @param {string|number} [params.typeVal] - 场所类型
 * @param {string[]} [params.keywords] - 关键词列表
 * @param {number} [params.limit=15] - 返回结果数量
 * @returns {Promise} 包含场所数据的Promise
 */
export const getSpotsByCriteria = async (params) => {
  const requestBody = {
    start_vertex_id: params.startVertexId,
    location_name: params.locationName,
    distance: params.distance,
    types: params.typeVal ? [params.typeVal] : [],
    keywords: params.keywords || [],
    limit: params.limit || 15
  }
  return axios.post('/api/path/spots-by-criteria', requestBody)
}
export function getAllTypes() {
  return axios.get('/api/path/all-types')
}
/**
 * 基于路径的附近场所查询
 * @param {Object} params - 查询参数
 * @param {number} [params.startVertexId] - 起始顶点ID
 * @param {string} [params.locationName] - 起始地点名称
 * @param {number} params.maxDistance - 最大距离（米）
 * @param {number} [params.strategy=0] - 路径策略
 * @returns {Promise} 包含场所数据的Promise
 */
export const getNearbySpotsByPath = async (params) => {
  const requestBody = {
    start_vertex_id: params.startVertexId,
    location_name: params.locationName,
    distance: params.maxDistance,
    strategy: params.strategy || 0
  }
  return axios.post('/api/path/nearby-spots', requestBody)
}

/**
 * 按名称搜索场所
 * @param {Object} params - 查询参数
 * @param {string} params.name - 场所名称关键词
 * @param {number} [params.type] - 场所类型
 * @param {number} [params.x] - 坐标X
 * @param {number} [params.y] - 坐标Y
 * @returns {Promise} 包含场所数据的Promise
 */
export const searchSpotsByName = async (params) => {
  return axios.get('/api/path/search-by-name', {
    params: {
      name: params.name,
      type: params.type,
      x: params.x,
      y: params.y
    }
  })
}

/**
 * 获取地点建议
 * @param {string} query - 搜索关键词
 * @param {number} [limit=10] - 返回建议数量
 * @returns {Promise} 包含建议数据的Promise
 */
export const getLocationSuggestions = async (query, limit = 10) => {
  return axios.get('/api/path/location-suggestions', {
    params: {
      q: query,
      limit: limit
    }
  })
}